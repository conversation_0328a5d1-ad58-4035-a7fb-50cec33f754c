import { supabase } from "@/integrations/supabase/client";
import type {
  CreateCustomerInput,
  Customer,
  CustomerDB,
  UpdateCustomerInput,
} from "@/lib/types";
import { convertCustomerFromDB } from "@/lib/types";

export const customerService = {
  async getAll(): Promise<Customer[]> {
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching customers:", error);
      throw error;
    }

    return ((data as CustomerDB[]) || []).map(convertCustomerFromDB);
  },

  async create(customer: CreateCustomerInput): Promise<Customer> {
    const { data, error } = await supabase
      .from("customers")
      .insert([customer])
      .select()
      .single();

    if (error) {
      console.error("Error creating customer:", error);
      throw error;
    }

    return convertCustomerFromDB(data as CustomerDB);
  },

  async update(id: string, customer: UpdateCustomerInput): Promise<Customer> {
    const { data, error } = await supabase
      .from("customers")
      .update(customer)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating customer:", error);
      throw error;
    }

    return convertCustomerFromDB(data as CustomerDB);
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("customers").delete().eq("id", id);

    if (error) {
      console.error("Error deleting customer:", error);
      throw error;
    }
  },
};
