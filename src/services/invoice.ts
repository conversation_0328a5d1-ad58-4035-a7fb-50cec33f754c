import { supabase } from "@/integrations/supabase/client";
import type {
  CreateInvoiceInput,
  CustomerDB,
  Invoice,
  InvoiceDB,
  InvoiceItemDB,
  UpdateInvoiceInput,
} from "@/lib/types";
import { convertInvoiceFromDB } from "@/lib/types";

export const invoiceService = {
  async getAll(): Promise<Invoice[]> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching invoices:", error);
      throw error;
    }

    type InvoiceWithRelations = InvoiceDB & {
      customer?: CustomerDB;
      items?: InvoiceItemDB[];
    };
    return ((data as InvoiceWithRelations[]) || []).map(convertInvoiceFromDB);
  },

  async getById(id: string): Promise<Invoice | null> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching invoice:", error);
      return null;
    }

    type InvoiceWithRelations = InvoiceDB & {
      customer?: CustomerDB;
      items?: InvoiceItemDB[];
    };
    return data ? convertInvoiceFromDB(data as InvoiceWithRelations) : null;
  },

  async create(invoice: CreateInvoiceInput): Promise<Invoice> {
    // Generate invoice number
    const { data: invoiceNumberData } = await supabase.rpc(
      "generate_invoice_number"
    );

    const invoiceNumber = invoiceNumberData || `INV${Date.now()}`;

    // Extract items from invoice data before inserting
    const { items, ...invoiceData } = invoice;

    const { data, error } = await supabase
      .from("invoices")
      .insert([
        {
          ...invoiceData,
          invoice_number: invoiceNumber,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating invoice:", error);
      throw error;
    }

    // Create invoice items if provided
    if (items && items.length > 0) {
      const itemsToInsert = items.map((item) => ({
        invoice_id: data.id,
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        price: item.price,
        amount: item.amount,
      }));

      const { error: itemsError } = await supabase
        .from("invoice_items")
        .insert(itemsToInsert);

      if (itemsError) {
        console.error("Error creating invoice items:", itemsError);
        throw itemsError;
      }
    }

    return convertInvoiceFromDB(data as InvoiceDB);
  },

  async update(id: string, invoice: UpdateInvoiceInput): Promise<Invoice> {
    // Extract items from invoice data before updating
    const { items, ...invoiceData } = invoice;

    const { data, error } = await supabase
      .from("invoices")
      .update(invoiceData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating invoice:", error);
      throw error;
    }

    // Update invoice items if provided
    if (items && items.length > 0) {
      // First delete existing items
      await supabase.from("invoice_items").delete().eq("invoice_id", id);

      // Then insert new items
      const itemsToInsert = items.map((item) => ({
        invoice_id: id,
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        price: item.price,
        amount: item.amount,
      }));

      const { error: itemsError } = await supabase
        .from("invoice_items")
        .insert(itemsToInsert);

      if (itemsError) {
        console.error("Error updating invoice items:", itemsError);
        throw itemsError;
      }
    }

    return convertInvoiceFromDB(data as InvoiceDB);
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("invoices").delete().eq("id", id);

    if (error) {
      console.error("Error deleting invoice:", error);
      throw error;
    }
  },
};
