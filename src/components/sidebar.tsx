import { FileText, LayoutDashboard, List, Users } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

const Sidebar = () => {
  const location = useLocation();

  const navItems = [
    { path: "/dashboard", label: "Dashboard", icon: LayoutDashboard },
    { path: "/create-invoice", label: "Create Invoice", icon: FileText },
    { path: "/invoices", label: "All Invoices", icon: List },
    { path: "/customers", label: "Customers", icon: Users },
  ];

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-white shadow-xl border-r border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-gray-800">Express Print</h1>
        <p className="text-sm text-gray-600">Invoice Management</p>
      </div>

      <nav className="mt-6">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                isActive
                  ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.label}
            </Link>
          );
        })}
      </nav>

      <div className="absolute bottom-6 left-6 right-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-xs text-gray-600">Express Print</p>
          <p className="text-xs text-gray-500">
            Panisheola Sitalatala, Haripal
          </p>
          <p className="text-xs text-gray-500">Phone: 9832407944</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
