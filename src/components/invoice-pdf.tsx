import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// Function to generate and download PDF using html2canvas
export const generateInvoicePDF = async (filename: string): Promise<void> => {
  try {
    // Get the invoice content element
    const element = document.getElementById("invoice-content");
    if (!element) {
      throw new Error("Invoice content element not found");
    }

    // Wait a bit for styles to be fully applied
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: element.scrollWidth,
      height: element.scrollHeight,
      logging: false, // Disable logging to avoid console spam
      ignoreElements: (element) => {
        // Skip elements that might cause issues
        return element.classList?.contains("print:hidden") || false;
      },
      onclone: (clonedDoc) => {
        // Force all elements to use computed styles instead of CSS custom properties
        const allElements = clonedDoc.querySelectorAll("*");
        allElements.forEach((el) => {
          const computedStyle = window.getComputedStyle(el as Element);

          // Apply computed styles directly
          (el as HTMLElement).style.color = computedStyle.color;
          (el as HTMLElement).style.backgroundColor =
            computedStyle.backgroundColor;
          (el as HTMLElement).style.borderColor = computedStyle.borderColor;
          (el as HTMLElement).style.borderTopColor =
            computedStyle.borderTopColor;
          (el as HTMLElement).style.borderRightColor =
            computedStyle.borderRightColor;
          (el as HTMLElement).style.borderBottomColor =
            computedStyle.borderBottomColor;
          (el as HTMLElement).style.borderLeftColor =
            computedStyle.borderLeftColor;
        });

        // Add fallback styles
        const style = clonedDoc.createElement("style");
        style.textContent = `
          /* Fallback colors */
          * {
            color: #000000 !important;
            border-color: #9ca3af !important;
          }

          .bg-white, #invoice-content {
            background-color: #ffffff !important;
          }

          .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }

          .border-gray-400, .border {
            border-color: #9ca3af !important;
          }

          .text-gray-500 {
            color: #6b7280 !important;
          }
        `;
        clonedDoc.head.appendChild(style);
      },
    });

    // Calculate PDF dimensions for standard invoice paper (8.5" x 11" / Letter size)
    const imgWidth = 215.9; // Letter width in mm (8.5 inches)
    const pageHeight = 279.4; // Letter height in mm (11 inches)
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    // Create PDF with Letter size (standard invoice paper)
    const pdf = new jsPDF("p", "mm", "letter");
    let position = 0;

    // Add the image to PDF
    pdf.addImage(
      canvas.toDataURL("image/png"),
      "PNG",
      0,
      position,
      imgWidth,
      imgHeight
    );
    heightLeft -= pageHeight;

    // Add additional pages if content is longer than one page
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      );
      heightLeft -= pageHeight;
    }

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  }
};
