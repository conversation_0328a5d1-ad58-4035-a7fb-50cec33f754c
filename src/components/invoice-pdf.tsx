import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// Function to convert oklch color to rgb
const oklchToRgb = (
  l: number,
  c: number,
  h: number
): [number, number, number] => {
  // Convert oklch to oklab
  const a = c * Math.cos((h * Math.PI) / 180);
  const b = c * Math.sin((h * Math.PI) / 180);

  // Convert oklab to linear RGB
  const l_ = l + 0.3963377774 * a + 0.2158037573 * b;
  const m_ = l - 0.1055613458 * a - 0.0638541728 * b;
  const s_ = l - 0.0894841775 * a - 1.291485548 * b;

  const l3 = l_ * l_ * l_;
  const m3 = m_ * m_ * m_;
  const s3 = s_ * s_ * s_;

  const r = +4.0767416621 * l3 - 3.3077115913 * m3 + 0.2309699292 * s3;
  const g = -1.2684380046 * l3 + 2.6097574011 * m3 - 0.3413193965 * s3;
  const b_rgb = -0.0041960863 * l3 - 0.7034186147 * m3 + 1.707614701 * s3;

  // Apply gamma correction
  const gamma = (x: number) => {
    return x >= 0.0031308 ? 1.055 * Math.pow(x, 1 / 2.4) - 0.055 : 12.92 * x;
  };

  return [
    Math.max(0, Math.min(255, Math.round(gamma(r) * 255))),
    Math.max(0, Math.min(255, Math.round(gamma(g) * 255))),
    Math.max(0, Math.min(255, Math.round(gamma(b_rgb) * 255))),
  ];
};

// Function to parse oklch color string and convert to hex
const parseOklchToHex = (oklchStr: string): string => {
  const match = oklchStr.match(/oklch\(\s*([^)]+)\s*\)/);
  if (!match) return oklchStr;

  const values = match[1].split(/\s+/);
  if (values.length < 3) return oklchStr;

  const l = parseFloat(values[0]) / 100; // Convert percentage to decimal
  const c = parseFloat(values[1]);
  const h = parseFloat(values[2]) || 0;

  const [r, g, b] = oklchToRgb(l, c, h);
  return `#${r.toString(16).padStart(2, "0")}${g
    .toString(16)
    .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
};

// Function to generate and download PDF using html2canvas
export const generateInvoicePDF = async (filename: string): Promise<void> => {
  try {
    // Get the invoice content element
    const element = document.getElementById("invoice-content");
    if (!element) {
      throw new Error("Invoice content element not found");
    }

    // Wait a bit for styles to be fully applied
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: element.scrollWidth,
      height: element.scrollHeight,
      logging: false, // Disable logging to avoid console spam
      ignoreElements: (element) => {
        // Skip elements that might cause issues
        return element.classList?.contains("print:hidden") || false;
      },
      onclone: (clonedDoc) => {
        // Convert all oklch colors to RGB equivalents while preserving exact appearance
        const allElements = clonedDoc.querySelectorAll("*");

        allElements.forEach((el) => {
          const computedStyle = window.getComputedStyle(el as Element);
          const htmlEl = el as HTMLElement;

          // Convert color properties that might use oklch
          const colorProperties = [
            "color",
            "backgroundColor",
            "borderColor",
            "borderTopColor",
            "borderRightColor",
            "borderBottomColor",
            "borderLeftColor",
            "outlineColor",
            "textDecorationColor",
            "caretColor",
          ];

          colorProperties.forEach((prop) => {
            const value = computedStyle.getPropertyValue(prop);
            if (value && value.includes("oklch")) {
              const convertedColor = parseOklchToHex(value);
              htmlEl.style.setProperty(prop, convertedColor, "important");
            }
          });
        });

        // Add comprehensive fallback styles for common Tailwind classes
        const style = clonedDoc.createElement("style");
        style.textContent = `
          /* Ensure all colors are in supported formats */
          * {
            color: inherit !important;
            border-color: inherit !important;
          }

          /* Standard colors for common elements */
          .bg-white, #invoice-content {
            background-color: #ffffff !important;
          }

          .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }

          .border-gray-400, .border {
            border-color: #9ca3af !important;
          }

          .text-gray-500 {
            color: #6b7280 !important;
          }

          .text-black {
            color: #000000 !important;
          }

          /* Ensure text is black by default */
          body, p, div, span, td, th {
            color: #000000 !important;
          }
        `;
        clonedDoc.head.appendChild(style);
      },
    });

    // Calculate PDF dimensions for standard invoice paper (8.5" x 11" / Letter size)
    const imgWidth = 215.9; // Letter width in mm (8.5 inches)
    const pageHeight = 279.4; // Letter height in mm (11 inches)
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    // Create PDF with Letter size (standard invoice paper)
    const pdf = new jsPDF("p", "mm", "letter");
    let position = 0;

    // Add the image to PDF
    pdf.addImage(
      canvas.toDataURL("image/png"),
      "PNG",
      0,
      position,
      imgWidth,
      imgHeight
    );
    heightLeft -= pageHeight;

    // Add additional pages if content is longer than one page
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      );
      heightLeft -= pageHeight;
    }

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  }
};
