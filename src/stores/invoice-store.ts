import type { Customer, InvoiceItem } from "@/lib/types";
import { create } from "zustand";
import { persist } from "zustand/middleware";

// Store-specific invoice interface for local state management
export interface StoreInvoice {
  id: string;
  invoiceNumber: string;
  date: string;
  customer: Customer;
  items: InvoiceItem[];
  subtotal: number;
  discount: number;
  discountPercentage: number;
  total: number;
  status: "paid" | "unpaid" | "pending";
  notes?: string;
}

interface InvoiceStore {
  invoices: StoreInvoice[];
  customers: Customer[];
  nextInvoiceNumber: number;
  addInvoice: (invoice: Omit<StoreInvoice, "id" | "invoiceNumber">) => string;
  updateInvoice: (id: string, invoice: Partial<StoreInvoice>) => void;
  deleteInvoice: (id: string) => void;
  addCustomer: (customer: Omit<Customer, "id">) => string;
  updateCustomer: (id: string, customer: Partial<Customer>) => void;
  deleteCustomer: (id: string) => void;
  getInvoiceById: (id: string) => StoreInvoice | undefined;
  getCustomerById: (id: string) => Customer | undefined;
  getStats: () => {
    totalInvoices: number;
    totalRevenue: number;
    monthlyInvoices: number;
    monthlyRevenue: number;
    totalCustomers: number;
    pendingInvoices: number;
  };
}

export const useInvoiceStore = create<InvoiceStore>()(
  persist(
    (set, get) => ({
      invoices: [],
      customers: [],
      nextInvoiceNumber: 1,

      addInvoice: (invoiceData) => {
        const id = `invoice-${Date.now()}`;
        const invoiceNumber = `INV${get()
          .nextInvoiceNumber.toString()
          .padStart(3, "0")}`;

        const invoice: StoreInvoice = {
          ...invoiceData,
          id,
          invoiceNumber,
        };

        set((state) => ({
          invoices: [...state.invoices, invoice],
          nextInvoiceNumber: state.nextInvoiceNumber + 1,
        }));

        return id;
      },

      updateInvoice: (id, invoiceData) => {
        set((state) => ({
          invoices: state.invoices.map((invoice) =>
            invoice.id === id ? { ...invoice, ...invoiceData } : invoice
          ),
        }));
      },

      deleteInvoice: (id) => {
        set((state) => ({
          invoices: state.invoices.filter((invoice) => invoice.id !== id),
        }));
      },

      addCustomer: (customerData) => {
        const id = `customer-${Date.now()}`;
        const customer: Customer = { ...customerData, id };

        set((state) => ({
          customers: [...state.customers, customer],
        }));

        return id;
      },

      updateCustomer: (id, customerData) => {
        set((state) => ({
          customers: state.customers.map((customer) =>
            customer.id === id ? { ...customer, ...customerData } : customer
          ),
        }));
      },

      deleteCustomer: (id) => {
        set((state) => ({
          customers: state.customers.filter((customer) => customer.id !== id),
        }));
      },

      getInvoiceById: (id) => {
        return get().invoices.find((invoice) => invoice.id === id);
      },

      getCustomerById: (id) => {
        return get().customers.find((customer) => customer.id === id);
      },

      getStats: () => {
        const { invoices, customers } = get();
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const monthlyInvoices = invoices.filter((invoice) => {
          const invoiceDate = new Date(invoice.date);
          return (
            invoiceDate.getMonth() === currentMonth &&
            invoiceDate.getFullYear() === currentYear
          );
        });

        return {
          totalInvoices: invoices.length,
          totalRevenue: invoices.reduce(
            (sum, invoice) => sum + invoice.total,
            0
          ),
          monthlyInvoices: monthlyInvoices.length,
          monthlyRevenue: monthlyInvoices.reduce(
            (sum, invoice) => sum + invoice.total,
            0
          ),
          totalCustomers: customers.length,
          pendingInvoices: invoices.filter(
            (invoice) =>
              invoice.status === "unpaid" || invoice.status === "pending"
          ).length,
        };
      },
    }),
    {
      name: "invoice-store",
    }
  )
);
