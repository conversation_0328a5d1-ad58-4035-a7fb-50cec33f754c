{"name": "express-print", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.79.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "zod": "^3.25.46", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}